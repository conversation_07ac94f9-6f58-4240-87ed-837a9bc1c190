<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme == 'theme-custom'
          ? settings.menuBackgroundCustom
          : settings.sideTheme === 'theme-dark'
          ? variables.menuBackground
          : settings.sideTheme === 'theme-theme'
          ? settings.theme
          : variables.menuLightBackground,
    }"
  >
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuBackgroundCustom
            : settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : settings.sideTheme === 'theme-theme'
            ? settings.theme
            : variables.menuLightBackground
        "
        :text-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuColorCustom
            : settings.sideTheme === 'theme-dark' ||
              settings.sideTheme === 'theme-theme'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
      <div
        class="title"
        style="
          font-size: 14px;
          padding-left: 20px;
          height: 50px;
          line-height: 50px;
          font-weight: bold;
        "
      >
        {{ $t("session.historySession") }}
      </div>

      <div class="chat-list">
        <div v-for="group in groupedSessions" :key="group.title">
          <div class="group-header" v-if="group.sessions.length">
            {{ group.title }}
          </div>
          <div
            class="chat-item"
            :class="{ 'is-active': i.conversationId == conversationId }"
            v-for="i in group.sessions"
            :key="i.conversationId"
            @click="openDify(i)"
          >
            <div class="app-icon">
              <div
                class="emoji-icon"
                :style="{ background: i.iconBackground }"
                v-if="i.iconType == 'emoji'"
              >
                {{ i.icon }}
              </div>
              <img v-else :src="i.sourceUrl" object-fit="contain" />
            </div>
            <div class="chat-item-name" :title="i.conversationName">
              {{ i.conversationName }}
            </div>
            <more-btns :row="i" :btns="btns" :limit="0" />
            <!-- <el-popconfirm
          placement="top"
          title="确定删除会话吗？"
          @confirm="deleteSession(i)"
        >
          <i
            class="chait-item-remove el-icon el-icon-close"
            slot="reference"
          ></i>
        </el-popconfirm> -->
          </div>
        </div>
      </div>
    </el-scrollbar>
    <sidebar-footer :collapse="isCollapse" />
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import SidebarItem from "../Sidebar/SidebarItem";
import variables from "@/assets/styles/variables.scss";
import sidebarFooter from "../Sidebar/siderFooter.vue";
import {
  getSessionList,
  deleteSession,
  editSession,
} from "@/api/ai/difySession";
export default {
  name: "AiSidebar",
  components: { SidebarItem, sidebarFooter },
  data() {
    return {
      // 自定义颜色
      menuColorCustom: this.$store.state.settings.menuColorCustom,
      menuColorActiveCustom: this.$store.state.settings.menuColorActiveCustom,
      menuBackgroundCustom: this.$store.state.settings.menuBackgroundCustom,
      sessionList: [],
      groupedSessions: [],
      btns: [
        {
          name: this.$t("common.rename"),
          id: "detailBtnId",
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.editName,
        },
        {
          name: this.$t("common.delete"),
          id: "detailBtnId",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.deleteSession,
        },
      ],
    };
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    conversationId() {
      const route = this.$route;
      return route?.query?.conversationId || "";
    },
    showLogo() {
      return false; //this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  created() {
    this.getSessionList();
  },
  methods: {
    getSessionList() {
      getSessionList({}).then((res) => {
        this.sessionList = res.data;
        this.groupByTime();
      });
    },
    groupByTime() {
      const now = new Date();
      const groups = [
        { title: this.$t("common.today"), sessions: [] },
        { title: this.$t("common.yesterday"), sessions: [] },
        { title: this.$t("common.beforeWeek"), sessions: [] },
        { title: this.$t("common.moreTime"), sessions: [] },
      ];
      this.sessionList.forEach((session) => {
        const timeDiff = now - new Date(session.updatedAt);
        const daysAgo = timeDiff / (1000 * 60 * 60 * 24);
        if (daysAgo < 1) {
          groups[0].sessions.push(session);
        } else if (daysAgo < 2) {
          groups[1].sessions.push(session);
        } else if (daysAgo < 7) {
          groups[2].sessions.push(session);
        } else {
          groups[3].sessions.push(session);
        }
      });
      this.groupedSessions = groups;
    },
    openDify(row) {
      // getAppDifyUrl({ appId: row.appId }).then((res) => {
      //更新应用会话ID
      let conversationIdInfo =
        JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
      conversationIdInfo[row.appId] = {
        ...conversationIdInfo[row.appId],
        [row.userId]: row.conversationId,
      };
      localStorage.setItem(
        "conversationIdInfo",
        JSON.stringify(conversationIdInfo),
      );
      this.$router.push({
        name: "difyPage",
        query: {
          // url: res.data,
          conversationId: row.conversationId,
          appId: row.appId,
        },
      });
      // });
    },
    deleteSession(row) {
      this.confirmMessage(this.$t("session.delTip")).then(() => {
        deleteSession({
          conversationId: row.conversationId,
          appId: row.appId,
        }).then((res) => {
          this.successMsg(this.$t("msg.delete"));
          this.getSessionList();
        });
      });
    },
    editName(row) {
      this.promptMessage(
        this.$t("session.pleaseEnterSession"),
        this.$t("common.tips"),
        {
          inputValue: row.conversationName,
          inputValidator: (val) => {
            if (!val) {
              return this.$t("placeholder.place");
            }
          },
        },
        true,
        false,
      ).then(({ value }) => {
        editSession({
          appId: row.appId,
          conversationId: row.conversationId,
          newName: value,
        }).then((res) => {
          this.successMsg(this.$t("msg.save"));
          this.getSessionList();
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.chat-list {
  .title {
    font-size: 12px;
    font-weight: bold;
    padding-left: 20px;
    color: #666666;
    height: 40px;
    line-height: 40px;
  }
  .group-header {
    font-size: 12px;
    font-weight: bold;
    padding-left: 20px;
    color: #666666;
    height: 24px;
    line-height: 24px;
  }
  span {
    padding-left: 0px;
  }
  .chat-item {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 6px;
    height: 50px;
    line-height: 50px;
    color: var(--custom-menu-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    &:hover {
      background-color: var(--custom-sub-menu-hover);
      color: var(--custom-menu-color);
    }
    &.is-active {
      background-color: var(--custom-sub-menu-background-active);
      color: var(--custom-menu-color-active);
    }
    .app-icon {
      flex: 0 0 30px;
      width: 30px;
      height: 30px;
      margin-right: 10px;
      .emoji-icon {
        display: inline-block;
        width: 100%;
        height: 30px;
        border-radius: 4px;
        line-height: 30px;
        text-align: center;
        font-size: 20px;
      }
      img {
        width: 100%;
        border-radius: 4px;
      }
    }
    .chat-item-name {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .chait-item-remove {
      padding: 0px 5px;
    }
  }
}
</style>
