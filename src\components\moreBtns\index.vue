<template>
  <div class="operate-btns">
    <el-button
      v-for="(i, idx) in hasRoleBtns"
      :style="{ display: i.hidden && i.hidden(row) ? 'none' : '' }"
      :key="idx"
      size="medium"
      type="text"
      :class="i.class"
      :id="i.id"
      :disabled="i.rule ? i.rule(row) : false"
      @click="i.func(row)"
      v-bind="i.attrs || {}"
      v-hasPermi="i.permi"
      >{{ i.name }}
    </el-button>
    <el-dropdown
      size="medium"
      v-hasPermi="morebtnPermi"
      v-if="hasRoleMoreBtns.length"
      trigger="hover"
      placement="bottom"
    >
      <span style="display: inline-block; padding-left: 5px; padding-right: 5px"
        ><i class="el-icon-more"></i
      ></span>
      <el-dropdown-menu class="operate-dropdown-more-btns" slot="dropdown">
        <el-dropdown-item
          v-for="(i, idx) in hasRoleMoreBtns"
          :key="idx"
          v-hasPermi="i.permi"
        >
          <el-button
            size="small"
            type="text"
            :class="i.class"
            :id="i.id"
            v-bind="i.attrs || {}"
            :disabled="i.rule ? i.rule(row) : false"
            :style="{ display: i.hidden && i.hidden(row) ? 'none' : '' }"
            v-hasPermi="i.permi"
            @click="i.func(row)"
            >{{ i.name }}
          </el-button>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: "moreBtns",
  props: {
    btns: {
      type: Array,
      default() {
        return [];
      },
    },
    row: {
      type: Object,
      default() {
        return {};
      },
    },
    limit: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    morebtnPermi() {
      let permi = [];
      this.hasRoleMoreBtns.forEach((i) => {
        permi = permi.concat(i.permi);
      });
      return permi;
    },
  },
  data() {
    return {
      hasRoleBtns: [],
      hasRoleMoreBtns: [],
    };
  },
  methods: {
    checkRuleBtn() {
      let hasRoleBtns = this.btns;
      if (this.limit >= 0) {
        if (hasRoleBtns.length > this.limit + 1) {
          this.hasRoleBtns = hasRoleBtns.slice(0, this.limit);
          this.hasRoleMoreBtns = hasRoleBtns.slice(this.limit);
        } else {
          this.hasRoleBtns = hasRoleBtns;
        }
      } else {
        this.hasRoleBtns = hasRoleBtns;
      }
    },
  },
  beforeMount() {
    this.checkRuleBtn();
  },
};
</script>
<style lang="scss" scoped>
.operate-btns {
  .el-button {
    padding: 2px 0px;
  }
  .el-dropdown {
    line-height: 1.5em;
  }
}
.operate-dropdown-more-btns {
  .el-dropdown-menu__item {
    .el-button {
      width: 100%;
      text-align: left;
      ::v-deep i {
        margin-right: 2px;
      }
      ::v-deep span {
        margin-left: 0px;
      }
    }
  }
}
</style>
